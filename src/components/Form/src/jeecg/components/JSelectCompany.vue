<!--单位选择组件-->
<template>
  <div class="JSelectCompany">
    <CompanyTreeSelect
      v-model:value="selectedValue"
      :placeholder="placeholder"
      :multiple="multiple"
      :allow-clear="allowClear"
      :show-search="showSearch"
      :check-strictly="checkStrictly"
      :label-in-value="labelInValue"
      :show-category="showCategory"
      :leaf-only="leafOnly"
      :filter-params="filterParams"
      :get-popup-container="getPopupContainer"
      @change="handleChange"
      @search="handleSearch"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue';
import { propTypes } from '/@/utils/propTypes';
import CompanyTreeSelect from './CompanyTreeSelect.vue';

interface Props {
  value?: string | string[] | any;
  placeholder?: string;
  // 是否允许多选，默认 false
  multiple?: boolean;
  // 是否允许清空，默认 true
  allowClear?: boolean;
  // 是否显示搜索框，默认 true
  showSearch?: boolean;
  // 多选时是否严格模式，默认 false
  checkStrictly?: boolean;
  // 是否返回标签值对象，默认 false
  labelInValue?: boolean;
  // 是否显示分类信息，默认 true
  showCategory?: boolean;
  // 是否只能选择叶子节点，默认 false
  leafOnly?: boolean;
  // 过滤条件
  filterParams?: Record<string, any>;
  // 获取弹出容器
  getPopupContainer?: (node: HTMLElement) => HTMLElement;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择单位',
  multiple: false,
  allowClear: true,
  showSearch: true,
  checkStrictly: false,
  labelInValue: false,
  showCategory: true,
  leafOnly: false,
});

const emit = defineEmits<{
  'update:value': [value: any];
  change: [value: any, label?: any, extra?: any];
  search: [value: string];
  select: [value: any, node?: any];
}>();

const selectedValue = ref();

// 监听外部value变化
watch(
  () => props.value,
  (newVal) => {
    selectedValue.value = newVal;
  },
  { immediate: true }
);

/**
 * 处理选择变化
 */
function handleChange(value: any, label?: any, extra?: any) {
  selectedValue.value = value;
  
  // 发出更新事件
  emit('update:value', value);
  emit('change', value, label, extra);
  
  // 兼容select事件
  if (value && !Array.isArray(value)) {
    emit('select', value, extra?.triggerNode);
  } else if (Array.isArray(value) && value.length > 0) {
    emit('select', value, extra?.triggerNode);
  } else {
    emit('select', null, null);
  }
}

/**
 * 处理搜索
 */
function handleSearch(value: string) {
  emit('search', value);
}

// 默认的弹出容器获取函数
const defaultGetPopupContainer = (node: HTMLElement) => {
  return node?.parentNode as HTMLElement || document.body;
};

const getPopupContainer = computed(() => {
  return props.getPopupContainer || defaultGetPopupContainer;
});
</script>

<script lang="ts">
export default {
  name: 'JSelectCompany',
  inheritAttrs: false,
};
</script>

<style scoped>
.JSelectCompany {
  width: 100%;
}
</style>
