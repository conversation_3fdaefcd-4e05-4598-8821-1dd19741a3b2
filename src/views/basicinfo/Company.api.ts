import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/basicinfo/company/rootList',
  save = '/basicinfo/company/add',
  edit = '/basicinfo/company/edit',
  deleteCompany = '/basicinfo/company/delete',
  importExcel = '/basicinfo/company/importExcel',
  exportXls = '/basicinfo/company/exportXls',
  loadTreeData = '/basicinfo/company/loadTreeRoot',
  getChildList = '/basicinfo/company/childList',
  getChildListBatch = '/basicinfo/company/getChildListBatch',
  getRootCompanyById = '/basicinfo/company/getRootCompanyById',
  loadTreeChildren = '/basicinfo/company/loadTreeChildren',
  searchCompany = '/basicinfo/company/searchCompany',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 * @param params
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除
 * @param params
 * @param handleSuccess
 */
export const deleteCompany = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteCompany, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDeleteCompany = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteCompany, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdateDict = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 查询全部树形节点数据
 * @param params
 */
export const loadTreeData = (params) => defHttp.get({ url: Api.loadTreeData, params });

/**
 * 查询子节点数据
 * @param params
 */
export const getChildList = (params) => defHttp.get({ url: Api.getChildList, params });

/**
 * 批量查询子节点数据
 * @param params
 */
export const getChildListBatch = (params) => defHttp.get({ url: Api.getChildListBatch, params }, { isTransformResponse: false });

/**
 * 查询根节点数据
 * @param params
 */
export const getRootCompanyById = (params) => defHttp.get({ url: Api.getRootCompanyById, params });

/**
 * 加载树形子节点数据
 * @param params
 */
export const loadTreeChildren = (params) => defHttp.get({ url: Api.loadTreeChildren, params });

/**
 * 搜索单位数据
 * @param params
 */
export const searchCompany = (params) => defHttp.get({ url: Api.searchCompany, params });
